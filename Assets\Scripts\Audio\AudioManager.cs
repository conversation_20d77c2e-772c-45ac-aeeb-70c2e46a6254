using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AudioManager : MonoBehaviour
{
    private static AudioManager _instance; // 单例模式，确保只有一个音频管理器实例
    public static AudioManager Instance
    {
        get { return _instance; }
    }
    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    public void PlayAudio(string audioPath)
    {
        //播放背景音乐
        //重复调用该函数时，上次播放的音乐会被覆盖
        AudioSource audioSource = gameObject.AddComponent<AudioSource>();
        audioSource.clip = Resources.Load<AudioClip>(audioPath);
        Debug.Log("Beat points loaded: " + _beatPoints.Count);
        audioSource.Play();
    }

    public void StopAudio()
    {
        //停止播放音乐
        AudioSource[] audioSources = GetComponents<AudioSource>();
        foreach (AudioSource audioSource in audioSources)
        {
            audioSource.Stop();
        }
    }

    public void PlayEffectSound(EffectSound effectSound)
    {
        // 创建临时AudioSource组件播放音效
        AudioSource audioSource = gameObject.AddComponent<AudioSource>();
        
        // 配置音效属性（根据EffectSound结构自定义）
        audioSource.clip = effectSound.clip;
        audioSource.volume = effectSound.volume;
        audioSource.pitch = effectSound.pitch;
        audioSource.loop = false;
        
        // 播放音效
        audioSource.Play();
        
        // 播放完成后自动销毁组件
        Destroy(audioSource, audioSource.clip.length);
    }
}
