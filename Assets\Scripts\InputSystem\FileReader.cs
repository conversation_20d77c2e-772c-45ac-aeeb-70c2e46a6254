using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

public class FileReader
{
    private static StreamReader Read(string path)
    {
        if (path == null)
            return null;
        if (!File.Exists(path))
            File.CreateText(path);
        return new StreamReader(path);
    }

    public static Dictionary<string, List<string>> ReadCsv(string path)
    {
        Dictionary<string, List<string>> csvData = new Dictionary<string, List<string>>();
        StreamReader stream = Read(path);

        if (stream == null)
            return csvData;

        string line;
        string[] headers = null;
        bool isFirstLine = true;

        while ((line = stream.ReadLine()) != null)
        {
            string[] values = line.Split(',');

            if (isFirstLine)
            {
                // 第一行作为列名称
                headers = values;
                foreach (string header in headers)
                {
                    csvData[header.Trim()] = new List<string>();
                }
                isFirstLine = false;
            }
            else
            {
                // 后续行作为数据
                for (int i = 0; i < values.Length && i < headers.Length; i++)
                {
                    csvData[headers[i].Trim()].Add(values[i].Trim());
                }
            }
        }

        stream.Close();
        stream.Dispose();
        return csvData;
    }
}