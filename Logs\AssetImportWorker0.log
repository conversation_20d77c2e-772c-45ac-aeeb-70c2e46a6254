Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.48f1 (b805b124c6b7) revision 12060081'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.48f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/荡剑行歌
-logFile
Logs/AssetImportWorker0.log
-srvPort
64216
Successfully changed project path to: H:/Works/荡剑行歌
H:/Works/荡剑行歌
Using Asset Import Pipeline V2.
Player connection [2712] Host "[IP] ************** [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [2712] Host "[IP] ************** [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 32.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1 (b805b124c6b7)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.48f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/荡剑行歌/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.48f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.48f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56940
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.48f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.000788 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 33.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.370 seconds
Domain Reload Profiling:
	ReloadAssembly (370ms)
		BeginReloadAssembly (47ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (280ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (85ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (114ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (33ms)
				BeforeProcessingInitializeOnLoad (8ms)
				ProcessInitializeOnLoadAttributes (52ms)
				ProcessInitializeOnLoadMethodAttributes (17ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002072 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 33.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.878 seconds
Domain Reload Profiling:
	ReloadAssembly (879ms)
		BeginReloadAssembly (98ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (14ms)
		EndReloadAssembly (746ms)
			LoadAssemblies (79ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (194ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (392ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (34ms)
				BeforeProcessingInitializeOnLoad (47ms)
				ProcessInitializeOnLoadAttributes (297ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 0.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2844 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 122.4 MB.
System memory in use after: 122.6 MB.

Unloading 28 unused Assets to reduce memory usage. Loaded Objects now: 3284.
Total: 2.718300 ms (FindLiveObjects: 0.148700 ms CreateObjectMapping: 0.055400 ms MarkObjects: 2.458700 ms  DeleteObjects: 0.054700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/LevelConfigs/Test.asset
  artifactKey: Guid(309fe8b8e2926724ba626a32ceacf64d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/LevelConfigs/Test.asset using Guid(309fe8b8e2926724ba626a32ceacf64d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6b447062adbe6382d5d6becb310c68f2') in 0.024648 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000154 seconds.
  path: Assets/Scenes/Combat.unity
  artifactKey: Guid(2cda990e2423bbf4892e6590ba056729) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Combat.unity using Guid(2cda990e2423bbf4892e6590ba056729) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '176cc27122fd53ed8956d48a543499d5') in 0.002004 seconds 
========================================================================
Received Import Request.
  Time since last request: 31.379478 seconds.
  path: Assets/Resources/Prefab/UI/Pendant.prefab
  artifactKey: Guid(6824e099d0916c14bb56ed6b296e6157) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefab/UI/Pendant.prefab using Guid(6824e099d0916c14bb56ed6b296e6157) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fcd384cded6c1f6d75a0254b4fe30274') in 0.095843 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.594694 seconds.
  path: Assets/Resources/Sprite/Char/Player/剑客侧面站立.png
  artifactKey: Guid(147b425abb9654d43a03c83bae3e7058) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Sprite/Char/Player/剑客侧面站立.png using Guid(147b425abb9654d43a03c83bae3e7058) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'db5bf4540862a5def051f412f40b6777') in 0.036421 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.592353 seconds.
  path: Assets/Scripts/Gameplay/ScenceController.cs
  artifactKey: Guid(9626b5bdc73606e4fae77c8452009cde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Gameplay/ScenceController.cs using Guid(9626b5bdc73606e4fae77c8452009cde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '440b509215d72deaad901faf406dd509') in 0.008747 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.502778 seconds.
  path: Assets/Scripts/Gameplay/CombatController.cs
  artifactKey: Guid(9e10478b89fa78f4fb090319061af2ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Gameplay/CombatController.cs using Guid(9e10478b89fa78f4fb090319061af2ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '93816436ac01c59d230b44e751ef0e72') in 0.003105 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.202954 seconds.
  path: Assets/Scripts/Data/GameTypes.cs
  artifactKey: Guid(636a2523878188d44bb1614acfd27a00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Data/GameTypes.cs using Guid(636a2523878188d44bb1614acfd27a00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e43e9ccbcdb72c3d1e2520ffd2b1e6b5') in 0.002313 seconds 
========================================================================
Received Import Request.
  Time since last request: 92.954859 seconds.
  path: Assets/Scripts/Gameplay/SceneController.cs
  artifactKey: Guid(9626b5bdc73606e4fae77c8452009cde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Gameplay/SceneController.cs using Guid(9626b5bdc73606e4fae77c8452009cde) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4908d2a72784793028a401a76f91a7be') in 0.002801 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.003580 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.980 seconds
Domain Reload Profiling:
	ReloadAssembly (980ms)
		BeginReloadAssembly (124ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (37ms)
		EndReloadAssembly (814ms)
			LoadAssemblies (110ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (212ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (415ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (345ms)
				ProcessInitializeOnLoadMethodAttributes (9ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2820 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 123.6 MB.
System memory in use after: 123.9 MB.

Unloading 15 unused Assets to reduce memory usage. Loaded Objects now: 3297.
Total: 5.277400 ms (FindLiveObjects: 0.323200 ms CreateObjectMapping: 0.263500 ms MarkObjects: 4.640500 ms  DeleteObjects: 0.048300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 70.672532 seconds.
  path: Assets/Scripts/Audio/AudioPlayer.cs
  artifactKey: Guid(3d1bdc1f6517df545bf0829ff8a55137) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Audio/AudioPlayer.cs using Guid(3d1bdc1f6517df545bf0829ff8a55137) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '125b82e5708e9b189252240450366f69') in 0.006036 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.003759 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.765 seconds
Domain Reload Profiling:
	ReloadAssembly (765ms)
		BeginReloadAssembly (87ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (645ms)
			LoadAssemblies (78ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (172ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (24ms)
			SetupLoadedEditorAssemblies (331ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (55ms)
				ProcessInitializeOnLoadAttributes (264ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2820 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 123.7 MB.
System memory in use after: 123.9 MB.

Unloading 15 unused Assets to reduce memory usage. Loaded Objects now: 3300.
Total: 3.467600 ms (FindLiveObjects: 0.401300 ms CreateObjectMapping: 0.077300 ms MarkObjects: 2.959500 ms  DeleteObjects: 0.027600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 12.978465 seconds.
  path: Assets/Scripts/Audio/AudioManager.cs
  artifactKey: Guid(3d1bdc1f6517df545bf0829ff8a55137) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Audio/AudioManager.cs using Guid(3d1bdc1f6517df545bf0829ff8a55137) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '20439409a1718ed708ed7475abef4fdf') in 0.005442 seconds 
========================================================================
Received Import Request.
  Time since last request: 27.522020 seconds.
  path: Assets/TextMesh Pro
  artifactKey: Guid(f54d1bd14bd3ca042bd867b519fee8cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro using Guid(f54d1bd14bd3ca042bd867b519fee8cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '16b4cbde9565bf190136f1148817e1ea') in 0.002694 seconds 
