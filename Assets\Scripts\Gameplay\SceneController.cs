using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SceneController : MonoBehaviour
{
    // 这个类用于实现场景控制器的逻辑
    // 这个类在每个场景具有唯一性

    private static SceneController _instance; // 单例模式，确保每个场景只有一个控制器实例

    private LevelConfig _sceneConfig;

    private List<float> _beatPoints; // 存储节拍点的列表，用于计算每次节拍结算的时间

    public LevelConfig SceneConfig
    {
        get { return _sceneConfig; }
    }
    public static SceneController Instance
    {
        get { return _instance; }
    }

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
        // 初始化场景配置
        LoadLevelConfig();
        InitializeBeatPoints();
    }

    // Start is called before the first frame update
    void Start()
    {
        LoadAudio();
        //LoadEnemy();
        //LoadPlayer();

    }


    // 初始化节拍点
    private void LoadLevelConfig()
    {
        //此处先试用测试数据
        _sceneConfig = Resources.Load<LevelConfig>("LevelConfigs/Test");
    }

    private void InitializeBeatPoints()
    {
        // 读取节拍点数据csv, 节拍文件为csv格式, 一行一个节拍点, 以秒为单位
        Dictionary<string, List<string>> beatPointsData = FileReader.ReadCsv(_sceneConfig.Beat_path);
        if (beatPointsData.Count == 0)
        {
            Debug.LogError("Beat points file not found.");
            return;
        }
        string[] beatPoints = beatPointsData["Time"].ToArray();
        _beatPoints = new List<float>();
        foreach (string beatPoint in beatPoints)
        {
            _beatPoints.Add(float.Parse(beatPoint));
        }
    }

    // 获取节拍点列表
    public List<float> GetBeatPoints()
    {
        return _beatPoints;
    }

    // 播放场景音乐
    private void LoadAudio()
    {
        //此处先试用测试数据
        AudioManager.Instance.PlayAudio(_sceneConfig.Audio_path);
    }
}
