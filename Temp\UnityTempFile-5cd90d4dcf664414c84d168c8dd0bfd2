/target:library
/out:Temp/Assembly-CSharp.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Assembly-CSharp.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.ComponentModel.Composition.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Core.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Data.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Drawing.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.IO.Compression.FileSystem.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Net.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Numerics.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.ServiceModel.Web.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Transactions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Web.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Windows.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Xml.Linq.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Xml.Serialization.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Xml.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/mscorlib.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.AppContext.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Console.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Data.Common.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Tracing.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Globalization.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.Compression.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.Pipes.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Http.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Ping.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Requests.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Security.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Sockets.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ObjectModel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Reflection.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Resources.Reader.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Resources.Writer.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Claims.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Principal.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.SecureString.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Text.Encoding.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Thread.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Timer.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ValueTuple.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XPath.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.48f1/Editor/Data/NetStandard/ref/2.0.0/netstandard.dll
/reference:Library/ScriptAssemblies/PsdPlugin.dll
/reference:Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.2D.Animation.Triangle.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.Common.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.2D.IK.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.2D.Path.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll
/reference:Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll
/reference:Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Addressables.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Addressables.dll
/reference:Library/ScriptAssemblies/Unity.InputSystem.dll
/reference:Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll
/reference:Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll
/reference:Library/ScriptAssemblies/Unity.Mathematics.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Mathematics.dll
/reference:Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll
/reference:Library/ScriptAssemblies/Unity.ResourceManager.dll
/reference:Library/ScriptAssemblies/Unity.Rider.Editor.dll
/reference:Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.Editor.dll
/reference:Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.dll
/reference:Library/ScriptAssemblies/Unity.Timeline.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Timeline.dll
/reference:Library/ScriptAssemblies/Unity.VSCode.Editor.dll
/reference:Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll
/reference:Library/ScriptAssemblies/UnityEditor.CacheServer.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_STANDARD_2_0
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_48
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
Assets\Scripts\Audio\AudioManager.cs
Assets\Scripts\Data\GameTypes.cs
Assets\Scripts\Data\LevelConfig.cs
Assets\Scripts\Events\GameEvents.cs
Assets\Scripts\Events\YinYangController.cs
Assets\Scripts\Gameplay\CombatController.cs
Assets\Scripts\Gameplay\EnemyController.cs
Assets\Scripts\Gameplay\PlayerActions.cs
Assets\Scripts\Gameplay\SceneController.cs
Assets\Scripts\Gameplay\YinYangRotate.cs
Assets\Scripts\SceneManagement\InitScence.cs
Assets\Scripts\UI\IrregularImage.cs
"Assets\TextMesh Pro\Examples & Extras\Scripts\Benchmark01.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\Benchmark01_UGUI.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\Benchmark02.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\Benchmark03.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\Benchmark04.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\CameraController.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\ChatController.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\DropdownSample.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\EnvMapAnimator.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\ObjectSpin.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\ShaderPropAnimator.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\SimpleScript.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\SkewTextExample.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_DigitValidator.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_ExampleScript_01.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_FrameRateCounter.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_PhoneNumberValidator.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_TextEventCheck.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_TextEventHandler.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_TextInfoDebugTool.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_TextSelector_A.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_TextSelector_B.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMP_UiFrameRateCounter.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TMPro_InstructionOverlay.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TeleType.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TextConsoleSimulator.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TextMeshProFloatingText.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\TextMeshSpawner.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\VertexColorCycler.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\VertexJitter.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\VertexShakeA.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\VertexShakeB.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\VertexZoom.cs"
"Assets\TextMesh Pro\Examples & Extras\Scripts\WarpTextExample.cs"
