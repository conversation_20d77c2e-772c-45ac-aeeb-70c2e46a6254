﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{364baa50-c494-3762-b43f-b1b9032b6b1a}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2020_3_48;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_STANDARD_2_0;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
     <Compile Include="Assets\Scripts\Audio\AudioManager.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SkewTextExample.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SimpleScript.cs" />
     <Compile Include="Assets\Scripts\Events\YinYangController.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01_UGUI.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_A.cs" />
     <Compile Include="Assets\Scripts\SceneManagement\InitScence.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeA.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark04.cs" />
     <Compile Include="Assets\Scripts\Gameplay\PlayerActions.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ChatController.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshProFloatingText.cs" />
     <Compile Include="Assets\Scripts\UI\IrregularImage.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeB.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\EnvMapAnimator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_UiFrameRateCounter.cs" />
     <Compile Include="Assets\Scripts\Data\GameTypes.cs" />
     <Compile Include="Assets\Scripts\Gameplay\CombatController.cs" />
     <Compile Include="Assets\Scripts\Events\GameEvents.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\CameraController.cs" />
     <Compile Include="Assets\Scripts\Gameplay\SceneController.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMPro_InstructionOverlay.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_ExampleScript_01.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextConsoleSimulator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ObjectSpin.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextInfoDebugTool.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventCheck.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ShaderPropAnimator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\WarpTextExample.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_DigitValidator.cs" />
     <Compile Include="Assets\Scripts\Data\LevelConfig.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark02.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshSpawner.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark03.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_FrameRateCounter.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexJitter.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventHandler.cs" />
     <Compile Include="Assets\Scripts\Gameplay\YinYangRotate.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_B.cs" />
     <Compile Include="Assets\Scripts\Gameplay\EnemyController.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\DropdownSample.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_PhoneNumberValidator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexColorCycler.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TeleType.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexZoom.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01.cs" />
     <Compile Include="Assets\Scripts\InputSystem\FileReader.cs" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
     <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Oswald-Bold - OFL.txt" />
     <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Anton OFL.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
     <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
     <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
     <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
     <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Bangers - OFL.txt" />
    <Reference Include="UnityEngine">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\ref\2.0.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
        <HintPath>H:\Works\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Animation.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Animation.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InternalAPIEngineBridge.001">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.InternalAPIEngineBridge.001.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.PixelPerfect">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.PixelPerfect.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Tilemap.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CacheServer">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\UnityEditor.CacheServer.dll</HintPath>
    </Reference>
    <Reference Include="PsdPlugin">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\PsdPlugin.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Addressables">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Addressables.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.IK.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.IK.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Psdimporter.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Psdimporter.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Path.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Path.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ResourceManager">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InternalAPIEditorBridge.001">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.InternalAPIEditorBridge.001.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.IK.Runtime">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.IK.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Animation.Triangle.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Animation.Triangle.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Animation.Runtime">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Animation.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.PixelPerfect.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.PixelPerfect.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Common.Runtime">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Common.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.SpriteShape.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Addressables.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.Addressables.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Common.Editor">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.Common.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Runtime">
        <HintPath>H:\Works\荡剑行歌\Library\ScriptAssemblies\Unity.2D.SpriteShape.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
